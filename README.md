# 夸克网盘上传工具 - 最终版本

一个专门用于夸克网盘文件上传的 Python 工具。支持单文件和批量上传，提供完善的配置管理和错误处理。

## 功能特性

- ✅ 文件上传到夸克网盘
- ✅ 支持批量上传
- ✅ 分片上传大文件（最大 10GB）
- ✅ 实时进度显示
- ✅ 配置文件管理
- ✅ 代理支持
- ✅ 重试机制
- ✅ 详细日志记录
- ✅ 命令行界面
- ⚠️ 分享链接需要手动创建（API 限制）

## 安装依赖

```bash
pip install requests tqdm pyyaml
```

## 配置设置

### 1. 获取 Cookie

1. 打开浏览器，登录夸克网盘 (https://pan.quark.cn)
2. 按 F12 打开开发者工具
3. 切换到 Network 标签
4. 刷新页面或进行任何操作
5. 找到任意一个请求，复制 Cookie 值
6. 将 Cookie 保存到 `quark.cookie.txt` 文件中

### 2. 配置文件

创建或修改 `config.yml` 文件：

```yaml
# 夸克网盘配置
quark:
  cookie_file: "quark.cookie.txt" # Cookie文件路径
  parent_dir_id: "0" # 上传目录ID (0为根目录)
  chunk_size: 8388608 # 分片大小 (8MB)
  timeout: 30 # 请求超时时间
  upload_timeout: 120 # 上传超时时间

# 分享配置
share:
  default_expire: 1 # 默认过期类型 (1=永久)
  url_type: 1 # 链接类型

# 代理配置 (可选)
proxy:
  enabled: false # 是否启用代理
  http: "" # HTTP代理地址
  https: "" # HTTPS代理地址

# 日志配置
logging:
  level: "INFO" # 日志级别
  to_file: false # 是否输出到文件
  file_path: "quark.log" # 日志文件路径
```

## 使用方法

### 推荐使用方式（最终版本）

```bash
# 上传单个文件
python quark_upload_final.py file.txt

# 批量上传多个文件
python quark_upload_final.py file1.txt file2.txt file3.txt

# 显示详细信息
python quark_upload_final.py file.txt --verbose

# 显示使用提示
python quark_upload_final.py --tips
```

### 其他可用工具

```bash
# 简化版上传工具
python simple_upload.py file.txt

# 完整功能版本（分享功能不可用）
python main.py upload file.txt

# 测试Cookie有效性
python test_cookie.py
```

### 手动创建分享链接

由于夸克网盘的分享 API 已不可用，需要手动创建分享链接：

1. **上传文件后**，工具会显示文件 ID 和上传成功信息
2. **打开夸克网盘网页版**: https://pan.quark.cn
3. **找到上传的文件**（通常在根目录）
4. **右键点击文件**，选择"分享"
5. **设置分享参数**：
   - 有效期：永久、7 天、1 天等
   - 访问权限：公开或密码访问
6. **创建并复制分享链接**

### Python 代码使用

```python
from quark import QuarkPan

# 创建客户端
quark = QuarkPan("config.yml")

# 仅上传文件（推荐方式）
upload_info = quark._pre_upload("test.txt")
quark._chunk_upload("test.txt", upload_info)
print(f"文件上传成功，文件ID: {upload_info['fid']}")

# 验证文件
if quark.validate_file("test.txt"):
    print("文件有效")

# 获取文件信息
info = quark.get_file_info("test.txt")
print(f"文件大小: {info['size_human']}")
print(f"文件类型: {info['mime_type']}")
```

## 文件结构

```
.
├── quark_upload_final.py    # 最终版上传工具（推荐使用）
├── simple_upload.py         # 简化版上传工具
├── main.py                  # 完整功能版本（分享功能不可用）
├── quark.py                 # 夸克网盘核心类
├── config_manager.py        # 配置管理器
├── exceptions.py            # 自定义异常类
├── test_cookie.py           # Cookie测试工具
├── test_share_api.py        # 分享API测试工具
├── config.yml               # 配置文件
├── quark.cookie.txt         # Cookie文件
├── requirements.txt         # 依赖包列表
└── README.md               # 说明文档
```

## 错误处理

工具提供了完善的错误处理机制：

- `AuthenticationError`: 认证失败
- `UploadError`: 上传错误
- `ShareError`: 分享错误
- `NetworkError`: 网络错误
- `FileValidationError`: 文件验证错误
- `APIError`: API 错误
- `RateLimitError`: 频率限制错误
- `QuotaExceededError`: 配额超出错误

## 注意事项

1. **Cookie 有效期**: Cookie 会过期，需要定期更新
2. **文件大小限制**: 单文件最大 10GB
3. **网络稳定性**: 大文件上传建议在稳定网络环境下进行
4. **频率限制**: 避免过于频繁的请求，可能被限制访问
5. **存储空间**: 确保网盘有足够的存储空间
6. **分享功能**: 由于 API 限制，分享链接需要手动在网页端创建
7. **完成上传 API**: 可能返回 400 错误，但不影响实际文件上传

## 常见问题

### Q: Cookie 过期怎么办？

A: 重新登录夸克网盘，按照配置步骤重新获取 Cookie。

### Q: 上传失败怎么办？

A: 检查网络连接、Cookie 有效性、文件大小和存储空间。

### Q: 如何设置代理？

A: 在 config.yml 中启用代理并设置代理地址。

### Q: 如何查看详细日志？

A: 使用 `--verbose` 参数或在配置文件中设置日志级别为 DEBUG。

## 许可证

MIT License
