# 夸克网盘上传分享工具

一个用于夸克网盘文件上传和分享链接生成的Python工具。支持单文件和批量上传，提供完善的配置管理和错误处理。

## 功能特性

- ✅ 文件上传到夸克网盘
- ✅ 自动生成分享链接
- ✅ 支持批量上传
- ✅ 分片上传大文件
- ✅ 进度显示
- ✅ 配置文件管理
- ✅ 代理支持
- ✅ 重试机制
- ✅ 详细日志记录
- ✅ 命令行界面

## 安装依赖

```bash
pip install requests tqdm pyyaml
```

## 配置设置

### 1. 获取Cookie

1. 打开浏览器，登录夸克网盘 (https://pan.quark.cn)
2. 按F12打开开发者工具
3. 切换到Network标签
4. 刷新页面或进行任何操作
5. 找到任意一个请求，复制Cookie值
6. 将Cookie保存到 `quark.cookie.txt` 文件中

### 2. 配置文件

创建或修改 `config.yml` 文件：

```yaml
# 夸克网盘配置
quark:
  cookie_file: "quark.cookie.txt"  # Cookie文件路径
  parent_dir_id: "0"               # 上传目录ID (0为根目录)
  chunk_size: 8388608              # 分片大小 (8MB)
  timeout: 30                      # 请求超时时间
  upload_timeout: 120              # 上传超时时间

# 分享配置
share:
  default_expire: 1                # 默认过期类型 (1=永久)
  url_type: 1                      # 链接类型

# 代理配置 (可选)
proxy:
  enabled: false                   # 是否启用代理
  http: ""                         # HTTP代理地址
  https: ""                        # HTTPS代理地址

# 日志配置
logging:
  level: "INFO"                    # 日志级别
  to_file: false                   # 是否输出到文件
  file_path: "quark.log"           # 日志文件路径
```

## 使用方法

### 命令行使用

```bash
# 上传单个文件
python main.py upload file.txt

# 上传多个文件
python main.py upload file1.txt file2.txt file3.txt

# 设置分享过期时间 (2=7天)
python main.py upload file.txt --expire 2

# 查看文件信息
python main.py info file.txt

# 查看分享选项
python main.py share-options

# 详细输出
python main.py upload file.txt --verbose
```

### 分享过期选项

- `0`: 180天 (SVIP可1年)
- `1`: 永久 (默认)
- `2`: 7天
- `3`: 1天
- `4`: 1小时

### Python代码使用

```python
from quark import QuarkPan

# 创建客户端
quark = QuarkPan("config.yml")

# 上传单个文件
result = quark.upload_file("test.txt", share_expire=1)
print(f"分享链接: {result['share_url']}")
print(f"提取码: {result['passwd']}")

# 批量上传
files = ["file1.txt", "file2.txt", "file3.txt"]
results = quark.upload_files(files)

for result in results:
    if result["status"] == "success":
        print(f"✅ {result['file_path']}: {result['share_url']}")
    else:
        print(f"❌ {result['file_path']}: {result['error']}")

# 验证文件
if quark.validate_file("test.txt"):
    print("文件有效")

# 获取文件信息
info = quark.get_file_info("test.txt")
print(f"文件大小: {info['size_human']}")
```

## 文件结构

```
.
├── main.py              # 主程序入口
├── quark.py             # 夸克网盘核心类
├── config_manager.py    # 配置管理器
├── exceptions.py        # 自定义异常类
├── config.yml           # 配置文件
├── quark.cookie.txt     # Cookie文件
└── README.md           # 说明文档
```

## 错误处理

工具提供了完善的错误处理机制：

- `AuthenticationError`: 认证失败
- `UploadError`: 上传错误
- `ShareError`: 分享错误
- `NetworkError`: 网络错误
- `FileValidationError`: 文件验证错误
- `APIError`: API错误
- `RateLimitError`: 频率限制错误
- `QuotaExceededError`: 配额超出错误

## 注意事项

1. **Cookie有效期**: Cookie会过期，需要定期更新
2. **文件大小限制**: 单文件最大10GB
3. **网络稳定性**: 大文件上传建议在稳定网络环境下进行
4. **频率限制**: 避免过于频繁的请求
5. **存储空间**: 确保网盘有足够的存储空间

## 常见问题

### Q: Cookie过期怎么办？
A: 重新登录夸克网盘，按照配置步骤重新获取Cookie。

### Q: 上传失败怎么办？
A: 检查网络连接、Cookie有效性、文件大小和存储空间。

### Q: 如何设置代理？
A: 在config.yml中启用代理并设置代理地址。

### Q: 如何查看详细日志？
A: 使用 `--verbose` 参数或在配置文件中设置日志级别为DEBUG。

## 许可证

MIT License
