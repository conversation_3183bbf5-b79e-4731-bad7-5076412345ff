#!/usr/bin/env python3
"""
夸克网盘上传和分享工具
主程序入口
"""

import argparse
import sys
import os
from pathlib import Path
from quark import QuarkPan
from exceptions import QuarkPanException
import logging

def setup_logging(verbose: bool = False):
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

def upload_single_file(quark: QuarkPan, file_path: str, share_expire: int = None):
    """上传单个文件"""
    try:
        print(f"📁 准备上传文件: {file_path}")
        
        # 验证文件
        if not quark.validate_file(file_path, raise_exception=True):
            return False
        
        # 获取文件信息
        file_info = quark.get_file_info(file_path)
        print(f"📊 文件大小: {file_info['size_human']}")
        print(f"📄 文件类型: {file_info['mime_type']}")
        
        # 上传文件并创建分享
        result = quark.upload_file(file_path, share_expire)
        
        # 显示结果
        print("\n✅ 上传成功！")
        print(quark.format_share_info(result))
        
        return True
        
    except QuarkPanException as e:
        print(f"❌ 上传失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def upload_multiple_files(quark: QuarkPan, file_paths: list, share_expire: int = None):
    """上传多个文件"""
    try:
        print(f"📁 准备批量上传 {len(file_paths)} 个文件")
        
        # 验证所有文件
        valid_files = []
        for file_path in file_paths:
            if quark.validate_file(file_path):
                valid_files.append(file_path)
            else:
                print(f"⚠️  跳过无效文件: {file_path}")
        
        if not valid_files:
            print("❌ 没有有效的文件可以上传")
            return False
        
        # 批量上传
        results = quark.upload_files(valid_files, share_expire)
        
        # 显示结果
        success_count = 0
        for result in results:
            if result["status"] == "success":
                success_count += 1
                print(f"\n✅ {result['file_path']} 上传成功")
                print(quark.format_share_info(result))
            else:
                print(f"\n❌ {result['file_path']} 上传失败: {result['error']}")
        
        print(f"\n📊 批量上传完成: {success_count}/{len(results)} 个文件成功")
        return success_count > 0
        
    except QuarkPanException as e:
        print(f"❌ 批量上传失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def show_file_info(quark: QuarkPan, file_path: str):
    """显示文件信息"""
    try:
        if not quark.validate_file(file_path, raise_exception=True):
            return False
        
        file_info = quark.get_file_info(file_path)
        
        print(f"""
📁 文件信息:
   名称: {file_info['name']}
   路径: {file_info['path']}
   大小: {file_info['size_human']} ({file_info['size']} bytes)
   类型: {file_info['mime_type']}
   修改时间: {file_info['modified_time']}
        """.strip())
        
        return True
        
    except QuarkPanException as e:
        print(f"❌ 获取文件信息失败: {e}")
        return False

def show_share_options():
    """显示分享选项"""
    quark = QuarkPan()
    options = quark.get_share_expire_options()
    
    print("📋 分享过期选项:")
    for code, desc in options.items():
        print(f"   {code}: {desc}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="夸克网盘上传和分享工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s upload file.txt                    # 上传单个文件
  %(prog)s upload file1.txt file2.txt        # 上传多个文件
  %(prog)s upload file.txt --expire 2        # 上传文件并设置7天过期
  %(prog)s info file.txt                     # 查看文件信息
  %(prog)s share-options                     # 查看分享选项
        """
    )
    
    parser.add_argument('command', choices=['upload', 'info', 'share-options'],
                       help='要执行的命令')
    parser.add_argument('files', nargs='*', help='文件路径')
    parser.add_argument('--config', '-c', default='config.yml',
                       help='配置文件路径 (默认: config.yml)')
    parser.add_argument('--expire', '-e', type=int,
                       help='分享过期类型 (0:180天, 1:永久, 2:7天)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    
    try:
        if args.command == 'share-options':
            show_share_options()
            return 0
        
        if args.command == 'info':
            if not args.files:
                print("❌ 请指定要查看信息的文件")
                return 1
            
            quark = QuarkPan(args.config)
            success = True
            for file_path in args.files:
                if not show_file_info(quark, file_path):
                    success = False
            
            return 0 if success else 1
        
        if args.command == 'upload':
            if not args.files:
                print("❌ 请指定要上传的文件")
                return 1
            
            quark = QuarkPan(args.config)
            
            if len(args.files) == 1:
                success = upload_single_file(quark, args.files[0], args.expire)
            else:
                success = upload_multiple_files(quark, args.files, args.expire)
            
            return 0 if success else 1
    
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
