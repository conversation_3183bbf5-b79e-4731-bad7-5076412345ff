"""夸克网盘相关异常类"""

class QuarkPanException(Exception):
    """夸克网盘基础异常类"""
    pass

class AuthenticationError(QuarkPanException):
    """认证错误"""
    pass

class UploadError(QuarkPanException):
    """上传错误"""
    pass

class ShareError(QuarkPanException):
    """分享错误"""
    pass

class ConfigError(QuarkPanException):
    """配置错误"""
    pass

class NetworkError(QuarkPanException):
    """网络错误"""
    pass

class FileValidationError(QuarkPanException):
    """文件验证错误"""
    pass

class APIError(QuarkPanException):
    """API错误"""
    def __init__(self, message: str, code: int = None):
        super().__init__(message)
        self.code = code

class RateLimitError(QuarkPanException):
    """频率限制错误"""
    pass

class QuotaExceededError(QuarkPanException):
    """配额超出错误"""
    pass
