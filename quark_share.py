#!/usr/bin/env python3
"""
夸克网盘文件上传分享工具
专注于上传文件并获取分享链接
"""

import os
import sys
import argparse
from quark import QuarkPan
from exceptions import QuarkPanException

def upload_and_share(file_path: str, 
                    expire_type: int = 1,
                    config_file: str = "config.yml",
                    verbose: bool = False) -> dict:
    """
    上传文件并创建分享链接
    
    Args:
        file_path: 文件路径
        expire_type: 过期类型 (0:180天, 1:永久, 2:7天)
        config_file: 配置文件路径
        verbose: 是否显示详细信息
        
    Returns:
        结果字典
    """
    try:
        # 检查文件
        if not os.path.exists(file_path):
            return {
                "success": False,
                "error": f"文件不存在: {file_path}",
                "file_path": file_path
            }
        
        # 创建夸克网盘客户端
        quark = QuarkPan(config_file)
        
        # 验证文件
        if not quark.validate_file(file_path):
            return {
                "success": False,
                "error": "文件验证失败",
                "file_path": file_path
            }
        
        # 获取文件信息
        file_info = quark.get_file_info(file_path)
        
        if verbose:
            print(f"📁 文件名: {file_info['name']}")
            print(f"📊 文件大小: {file_info['size_human']}")
            print(f"📄 文件类型: {file_info['mime_type']}")
            print()
        
        print(f"🚀 开始上传文件: {file_info['name']}")
        
        # 上传文件并创建分享链接
        result = quark.upload_file(file_path, expire_type)
        
        if result.get("status") == "success":
            print("✅ 上传和分享成功！")
            print(f"🔗 分享链接: {result['share_url']}")
            print(f"🔑 提取码: {result['passwd']}")
            
            return {
                "success": True,
                "file_path": file_path,
                "file_name": file_info['name'],
                "share_url": result['share_url'],
                "passwd": result['passwd'],
                "title": result['title']
            }
        else:
            error_msg = result.get("error", "分享链接创建失败")
            print(f"⚠️  文件上传成功，但{error_msg}")
            
            return {
                "success": False,
                "error": error_msg,
                "file_path": file_path,
                "file_uploaded": True
            }
            
    except QuarkPanException as e:
        error_msg = f"夸克网盘错误: {e}"
        print(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "file_path": file_path
        }
    except Exception as e:
        error_msg = f"未知错误: {e}"
        print(f"❌ {error_msg}")
        return {
            "success": False,
            "error": error_msg,
            "file_path": file_path
        }

def batch_upload_and_share(file_paths: list, **kwargs) -> list:
    """批量上传文件并创建分享链接"""
    results = []
    total = len(file_paths)
    
    print(f"📦 开始批量上传分享 {total} 个文件")
    print("=" * 60)
    
    success_count = 0
    for i, file_path in enumerate(file_paths, 1):
        print(f"\n--- 第 {i}/{total} 个文件 ---")
        
        result = upload_and_share(file_path, **kwargs)
        results.append(result)
        
        if result["success"]:
            success_count += 1
    
    print(f"\n📊 批量处理完成: {success_count}/{total} 个文件成功")
    
    # 显示汇总结果
    if success_count > 0:
        print("\n🎉 成功的分享链接:")
        for i, result in enumerate(results, 1):
            if result["success"]:
                print(f"{i}. {result['file_name']}")
                print(f"   🔗 {result['share_url']}")
                print(f"   🔑 {result['passwd']}")
    
    return results

def show_expire_options():
    """显示过期选项"""
    options = {
        0: "180天 (SVIP可1年)",
        1: "永久 (默认)",
        2: "7天",
        3: "1天",
        4: "1小时"
    }
    
    print("📋 分享过期选项:")
    for code, desc in options.items():
        print(f"   {code}: {desc}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="夸克网盘文件上传分享工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s file.txt                          # 上传文件并创建永久分享链接
  %(prog)s file1.txt file2.txt               # 批量上传多个文件
  %(prog)s file.txt --expire 2               # 创建7天过期的分享链接
  %(prog)s file.txt --verbose                # 显示详细信息
  %(prog)s --show-options                    # 显示过期选项
        """
    )
    
    parser.add_argument('files', nargs='*', help='要上传的文件路径')
    parser.add_argument('--expire', '-e', type=int, default=1,
                       help='分享过期类型 (0:180天, 1:永久, 2:7天, 默认:1)')
    parser.add_argument('--config', '-c', default='config.yml',
                       help='配置文件路径 (默认: config.yml)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细信息')
    parser.add_argument('--show-options', action='store_true',
                       help='显示分享过期选项')
    
    args = parser.parse_args()
    
    # 显示过期选项
    if args.show_options:
        show_expire_options()
        return 0
    
    # 检查文件参数
    if not args.files:
        print("❌ 请指定要上传的文件")
        parser.print_help()
        return 1
    
    # 检查文件是否存在
    missing_files = [f for f in args.files if not os.path.exists(f)]
    if missing_files:
        print("❌ 以下文件不存在:")
        for f in missing_files:
            print(f"   - {f}")
        return 1
    
    print("夸克网盘文件上传分享工具")
    print("=" * 50)
    
    try:
        if len(args.files) == 1:
            # 单文件上传
            result = upload_and_share(
                args.files[0],
                expire_type=args.expire,
                config_file=args.config,
                verbose=args.verbose
            )
            return 0 if result["success"] else 1
        else:
            # 批量上传
            results = batch_upload_and_share(
                args.files,
                expire_type=args.expire,
                config_file=args.config,
                verbose=args.verbose
            )
            success_count = sum(1 for r in results if r["success"])
            return 0 if success_count > 0 else 1
    
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
