# 夸克网盘转存工具配置文件

# API配置
api:
  # 用户调用接口token（在用户中心-个人信息获取）
  token: "your_token_here"
  
  # cookie管理中的ID（在用户中心-API管理获取）
  cookie_id: 1
  
  # API基础地址
  base_url: "https://api.moetp.com/quark/"

# 默认转存配置
transfer:
  # 默认保存目录ID（空字符串表示根目录）
  default_fid: ""
  
  # 默认去重方式
  # 1: 删除原文件后再转存（默认）
  # 2: 修改原文件名后再转存
  # 3: 将原文件移动到待删除目录后再转存
  # 4: 忽略删除原文件的错误，继续转存
  # 5: 纯转存模式，不检测重复文件，不记录到数据库
  default_repeat: 1

# 默认分享配置
share:
  # 是否默认创建分享链接
  auto_share: false
  
  # 默认分享类型
  # 1: 公开分享
  # 2: 密码分享
  default_url_type: 1
  
  # 默认有效期
  # 1: 永久
  # 2: 1天
  # 3: 7天
  # 4: 30天
  default_time: 1
  
  # 默认提取码（仅url_type=2时有效，空字符串表示自动生成）
  default_code: ""

# 批量转存配置
batch:
  # 批量转存时的延迟间隔（秒）
  delay: 1
  
  # 最大重试次数
  max_retries: 3
  
  # 重试延迟（秒）
  retry_delay: 2

# 日志配置
logging:
  # 是否启用详细日志
  verbose: true
  
  # 是否保存日志到文件
  save_to_file: false
  
  # 日志文件路径
  log_file: "transfer.log"
