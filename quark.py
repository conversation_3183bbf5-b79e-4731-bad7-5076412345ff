import hashlib, json, mimetypes, os, time, base64, requests, math, logging
from tqdm import tqdm
from typing import Optional, Dict, Any
from config_manager import ConfigManager
from exceptions import (
    QuarkPanException, AuthenticationError, UploadError, ShareError,
    NetworkError, FileValidationError, APIError, RateLimitError, QuotaExceededError
)

class QuarkPan:
    """夸克网盘上传和分享工具类"""

    def __init__(self, config_file: str = "config.yml"):
        """
        初始化夸克网盘客户端

        Args:
            config_file: 配置文件路径
        """
        # 加载配置
        self.config_manager = ConfigManager(config_file)
        quark_config = self.config_manager.get_quark_config()

        self.cookie_file = quark_config.get("cookie_file", "quark.cookie.txt")
        self.parent_dir_id = quark_config.get("parent_dir_id", "0")
        self.api_base = "https://drive-pc.quark.cn/1/clouddrive"
        self.chunk_size = quark_config.get("chunk_size", 8 * 1024 * 1024)
        self.timeout = quark_config.get("timeout", 30)
        self.upload_timeout = quark_config.get("upload_timeout", 120)

        # 读取cookie
        try:
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                self.cookie = f.read().strip()
        except FileNotFoundError:
            raise FileNotFoundError(f"Cookie文件 {self.cookie_file} 不存在")

        # 设置请求头
        self.headers = {
            "Cookie": self.cookie,
            "Referer": "https://pan.quark.cn",
            "Origin": "https://pan.quark.cn",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Content-Type": "application/json;charset=UTF-8",
            "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin"
        }

        self.params = {"pr": "ucpro", "fr": "pc"}

        # 创建session
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 设置代理
        proxy_config = self.config_manager.get_proxy_config()
        if proxy_config:
            self.session.proxies.update(proxy_config)

        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)

    def _setup_logging(self):
        """设置日志配置"""
        log_config = self.config_manager.get_logging_config()
        level = getattr(logging, log_config.get("level", "INFO").upper())
        format_str = log_config.get("format", "%(asctime)s - %(levelname)s - %(message)s")

        # 配置根日志记录器
        logging.basicConfig(level=level, format=format_str, force=True)

        # 如果需要输出到文件
        if log_config.get("to_file", False):
            file_handler = logging.FileHandler(log_config.get("file_path", "quark.log"), encoding='utf-8')
            file_handler.setFormatter(logging.Formatter(format_str))
            logging.getLogger().addHandler(file_handler)

    def _post(self, path: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送POST请求到夸克网盘API

        Args:
            path: API路径
            data: 请求数据

        Returns:
            响应数据
        """
        retry_config = self.config_manager.get_retry_config()
        max_attempts = retry_config.get("max_attempts", 3)
        delay = retry_config.get("delay", 1)
        backoff = retry_config.get("backoff", 2)

        for attempt in range(max_attempts):
            try:
                url = self.api_base + path
                response = self.session.post(url, params=self.params, json=data, timeout=self.timeout)
                response.raise_for_status()

                result = response.json()
                if result.get("code") != 0:
                    error_msg = result.get("message", "未知错误")
                    error_code = result.get("code")
                    self.logger.error(f"API请求失败: {error_msg} (code: {error_code})")

                    # 根据错误码抛出不同的异常
                    if error_code in [401, 403]:
                        raise AuthenticationError(f"认证失败: {error_msg}")
                    elif error_code == 429:
                        raise RateLimitError(f"请求频率过高: {error_msg}")
                    elif error_code in [507, 413]:
                        raise QuotaExceededError(f"存储空间不足: {error_msg}")
                    else:
                        raise APIError(error_msg, error_code)

                return result["data"]
            except requests.exceptions.RequestException as e:
                if attempt < max_attempts - 1:
                    self.logger.warning(f"请求失败，{delay}秒后重试 (第{attempt+1}/{max_attempts}次): {e}")
                    time.sleep(delay)
                    delay *= backoff
                else:
                    self.logger.error(f"网络请求失败: {e}")
                    raise NetworkError(f"网络请求失败: {e}")
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败: {e}")
                raise APIError(f"响应格式错误: {e}")

    def upload_file(self, file_path: str, share_expire: Optional[int] = None) -> Dict[str, str]:
        """
        上传文件并创建分享链接

        Args:
            file_path: 要上传的文件路径
            share_expire: 分享过期类型 (0:180天, 1:永久, 2:7天)，None时使用配置默认值

        Returns:
            包含分享链接和提取码的字典
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 使用配置的默认分享过期时间
        if share_expire is None:
            share_config = self.config_manager.get_share_config()
            share_expire = share_config.get("default_expire", 1)

        self.logger.info(f"开始上传文件: {file_path}")

        # ① 预上传
        upload_info = self._pre_upload(file_path)

        # ② 分片上传
        self._chunk_upload(file_path, upload_info)

        # ③ 提交分片
        self._finish_upload(upload_info)

        # ④ 创建分享
        share_info = self._create_share(upload_info["fid"], file_path, share_expire)

        self.logger.info("文件上传和分享创建完成")
        return share_info

    def _pre_upload(self, file_path: str) -> Dict[str, Any]:
        """预上传，获取上传参数"""
        size = os.path.getsize(file_path)
        mime = mimetypes.guess_type(file_path)[0] or "application/octet-stream"

        self.logger.info(f"文件大小: {size} bytes, MIME类型: {mime}")

        pre_data = {
            "pdir_fid": self.parent_dir_id,
            "file_name": os.path.basename(file_path),
            "size": size,
            "format_type": mime,
            "ccp_hash_update": True
        }

        result = self._post("/file/upload/pre", pre_data)

        upload_info = {
            "task_id": result["task_id"],
            "upload_id": result["upload_id"],
            "bucket": result["bucket"],
            "obj_key": result["obj_key"],
            "upload_host": f"https://{result['bucket']}.{result['upload_url'][7:]}",
            "fid": result["fid"],
            "size": size
        }

        self.logger.info("预上传完成，获取上传参数")
        return upload_info

    def _chunk_upload(self, file_path: str, upload_info: Dict[str, Any]) -> None:
        """分片上传文件"""
        etags = []
        size = upload_info["size"]

        with open(file_path, "rb") as f, tqdm(total=size, unit="B", unit_scale=True, desc="上传进度") as bar:
            part_num = 1
            while chunk := f.read(self.chunk_size):
                part_url = f"{upload_info['upload_host']}/{upload_info['obj_key']}"
                params = {"partNumber": part_num, "uploadId": upload_info["upload_id"]}

                # 创建专门用于上传的session，不使用夸克网盘的请求头
                upload_session = requests.Session()

                try:
                    response = upload_session.put(part_url, params=params, data=chunk, timeout=self.upload_timeout)
                    response.raise_for_status()
                    etags.append(response.headers["ETag"])
                    bar.update(len(chunk))
                    part_num += 1
                except requests.exceptions.RequestException as e:
                    self.logger.error(f"分片上传失败: {e}")
                    self.logger.error(f"上传URL: {part_url}")
                    self.logger.error(f"参数: {params}")
                    raise UploadError(f"分片上传失败: {e}")

        self.logger.info(f"分片上传完成，共上传 {part_num-1} 个分片")

    def _finish_upload(self, upload_info: Dict[str, Any]) -> None:
        """完成上传"""
        finish_data = {
            "obj_key": upload_info["obj_key"],
            "task_id": upload_info["task_id"]
        }

        self._post("/file/upload/finish", finish_data)
        self.logger.info("上传完成")

    def _create_share(self, fid: str, file_path: str, share_expire: int) -> Dict[str, str]:
        """创建分享链接"""
        share_data = {
            "fid_list": [fid],
            "title": os.path.basename(file_path),
            "expired_type": share_expire,
            "url_type": 1  # 标准短链
        }

        result = self._post("/share", share_data)

        share_info = {
            "share_url": result["share_url"],
            "passwd": result.get("passwd", "无"),
            "title": share_data["title"]
        }

        self.logger.info(f"分享链接创建成功: {share_info['share_url']}")
        return share_info

    def upload_files(self, file_paths: list, share_expire: Optional[int] = None) -> list:
        """
        批量上传文件并创建分享链接

        Args:
            file_paths: 要上传的文件路径列表
            share_expire: 分享过期类型

        Returns:
            包含每个文件分享信息的列表
        """
        results = []
        total_files = len(file_paths)

        self.logger.info(f"开始批量上传 {total_files} 个文件")

        for i, file_path in enumerate(file_paths, 1):
            try:
                self.logger.info(f"正在上传第 {i}/{total_files} 个文件: {file_path}")
                result = self.upload_file(file_path, share_expire)
                result["status"] = "success"
                result["file_path"] = file_path
                results.append(result)
            except Exception as e:
                self.logger.error(f"文件上传失败 {file_path}: {e}")
                results.append({
                    "status": "failed",
                    "file_path": file_path,
                    "error": str(e)
                })

        success_count = sum(1 for r in results if r["status"] == "success")
        self.logger.info(f"批量上传完成: {success_count}/{total_files} 个文件成功")

        return results

    def validate_file(self, file_path: str, raise_exception: bool = False) -> bool:
        """
        验证文件是否可以上传

        Args:
            file_path: 文件路径
            raise_exception: 是否抛出异常而不是返回False

        Returns:
            是否可以上传
        """
        try:
            if not os.path.exists(file_path):
                error_msg = f"文件不存在: {file_path}"
                self.logger.error(error_msg)
                if raise_exception:
                    raise FileValidationError(error_msg)
                return False

            if not os.path.isfile(file_path):
                error_msg = f"不是文件: {file_path}"
                self.logger.error(error_msg)
                if raise_exception:
                    raise FileValidationError(error_msg)
                return False

            file_size = os.path.getsize(file_path)
            if file_size == 0:
                error_msg = f"文件为空: {file_path}"
                self.logger.error(error_msg)
                if raise_exception:
                    raise FileValidationError(error_msg)
                return False

            # 检查文件大小限制 (夸克网盘单文件最大10GB)
            max_size = 10 * 1024 * 1024 * 1024  # 10GB
            if file_size > max_size:
                error_msg = f"文件过大 ({file_size} bytes > {max_size} bytes): {file_path}"
                self.logger.error(error_msg)
                if raise_exception:
                    raise FileValidationError(error_msg)
                return False

            return True
        except OSError as e:
            error_msg = f"文件访问错误: {e}"
            self.logger.error(error_msg)
            if raise_exception:
                raise FileValidationError(error_msg)
            return False

    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息

        Args:
            file_path: 文件路径

        Returns:
            文件信息字典
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        stat = os.stat(file_path)
        mime_type = mimetypes.guess_type(file_path)[0] or "application/octet-stream"

        return {
            "name": os.path.basename(file_path),
            "path": file_path,
            "size": stat.st_size,
            "size_human": self._format_size(stat.st_size),
            "mime_type": mime_type,
            "modified_time": time.ctime(stat.st_mtime),
            "created_time": time.ctime(stat.st_ctime)
        }

    def _format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"

    def create_share_only(self, fid: str, title: str, share_expire: Optional[int] = None) -> Dict[str, str]:
        """
        仅创建分享链接（文件已存在于网盘中）

        Args:
            fid: 文件ID
            title: 分享标题
            share_expire: 分享过期类型

        Returns:
            分享信息字典
        """
        if share_expire is None:
            share_config = self.config_manager.get_share_config()
            share_expire = share_config.get("default_expire", 1)

        return self._create_share(fid, title, share_expire)

    def get_share_expire_options(self) -> Dict[int, str]:
        """
        获取分享过期选项

        Returns:
            过期选项字典
        """
        return {
            0: "180天 (SVIP可1年)",
            1: "永久",
            2: "7天",
            3: "1天",
            4: "1小时"
        }

    def format_share_info(self, share_info: Dict[str, str]) -> str:
        """
        格式化分享信息为可读字符串

        Args:
            share_info: 分享信息字典

        Returns:
            格式化的分享信息
        """
        title = share_info.get("title", "未知文件")
        url = share_info.get("share_url", "")
        passwd = share_info.get("passwd", "无")

        return f"""
📁 文件名: {title}
🔗 分享链接: {url}
🔑 提取码: {passwd}
        """.strip()

    def batch_create_shares(self, file_infos: list, share_expire: Optional[int] = None) -> list:
        """
        批量创建分享链接

        Args:
            file_infos: 文件信息列表，每个元素包含fid和title
            share_expire: 分享过期类型

        Returns:
            分享信息列表
        """
        results = []
        total_files = len(file_infos)

        self.logger.info(f"开始批量创建 {total_files} 个分享链接")

        for i, file_info in enumerate(file_infos, 1):
            try:
                fid = file_info["fid"]
                title = file_info["title"]

                self.logger.info(f"正在创建第 {i}/{total_files} 个分享链接: {title}")
                result = self.create_share_only(fid, title, share_expire)
                result["status"] = "success"
                results.append(result)
            except Exception as e:
                self.logger.error(f"分享链接创建失败 {file_info.get('title', 'unknown')}: {e}")
                results.append({
                    "status": "failed",
                    "title": file_info.get("title", "unknown"),
                    "error": str(e)
                })

        success_count = sum(1 for r in results if r["status"] == "success")
        self.logger.info(f"批量分享创建完成: {success_count}/{total_files} 个成功")

        return results
