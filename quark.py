import hashlib, json, mimetypes, os, time, base64, requests, math, logging
from tqdm import tqdm
from typing import Optional, Dict, Any
from config_manager import ConfigManager
from exceptions import (
    QuarkPanException, AuthenticationError, UploadError, ShareError,
    NetworkError, FileValidationError, APIError, RateLimitError, QuotaExceededError
)

class QuarkPan:
    """夸克网盘上传和分享工具类"""

    def __init__(self, config_file: str = "config.yml"):
        """
        初始化夸克网盘客户端

        Args:
            config_file: 配置文件路径
        """
        # 加载配置
        self.config_manager = ConfigManager(config_file)
        quark_config = self.config_manager.get_quark_config()

        self.cookie_file = quark_config.get("cookie_file", "quark.cookie.txt")
        self.parent_dir_id = quark_config.get("parent_dir_id", "0")
        self.api_base = "https://drive-pc.quark.cn/1/clouddrive"
        self.chunk_size = quark_config.get("chunk_size", 8 * 1024 * 1024)
        self.timeout = quark_config.get("timeout", 30)
        self.upload_timeout = quark_config.get("upload_timeout", 120)

        # 读取cookie
        try:
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                self.cookie = f.read().strip()
        except FileNotFoundError:
            raise FileNotFoundError(f"Cookie文件 {self.cookie_file} 不存在")

        # 设置请求头
        self.headers = {
            "Cookie": self.cookie,
            "Referer": "https://pan.quark.cn",
            "Origin": "https://pan.quark.cn",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Content-Type": "application/json;charset=UTF-8",
            "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin"
        }

        self.params = {"pr": "ucpro", "fr": "pc"}

        # 创建session
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 设置代理
        proxy_config = self.config_manager.get_proxy_config()
        if proxy_config:
            self.session.proxies.update(proxy_config)

        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)

    def _setup_logging(self):
        """设置日志配置"""
        log_config = self.config_manager.get_logging_config()
        level = getattr(logging, log_config.get("level", "INFO").upper())
        format_str = log_config.get("format", "%(asctime)s - %(levelname)s - %(message)s")

        # 配置根日志记录器
        logging.basicConfig(level=level, format=format_str, force=True)

        # 如果需要输出到文件
        if log_config.get("to_file", False):
            file_handler = logging.FileHandler(log_config.get("file_path", "quark.log"), encoding='utf-8')
            file_handler.setFormatter(logging.Formatter(format_str))
            logging.getLogger().addHandler(file_handler)

    def _post(self, path: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送POST请求到夸克网盘API

        Args:
            path: API路径
            data: 请求数据

        Returns:
            响应数据
        """
        retry_config = self.config_manager.get_retry_config()
        max_attempts = retry_config.get("max_attempts", 3)
        delay = retry_config.get("delay", 1)
        backoff = retry_config.get("backoff", 2)

        for attempt in range(max_attempts):
            try:
                url = self.api_base + path
                response = self.session.post(url, params=self.params, json=data, timeout=self.timeout)
                response.raise_for_status()

                result = response.json()
                if result.get("code") != 0:
                    error_msg = result.get("message", "未知错误")
                    error_code = result.get("code")
                    self.logger.error(f"API请求失败: {error_msg} (code: {error_code})")

                    # 根据错误码抛出不同的异常
                    if error_code in [401, 403]:
                        raise AuthenticationError(f"认证失败: {error_msg}")
                    elif error_code == 429:
                        raise RateLimitError(f"请求频率过高: {error_msg}")
                    elif error_code in [507, 413]:
                        raise QuotaExceededError(f"存储空间不足: {error_msg}")
                    else:
                        raise APIError(error_msg, error_code)

                return result["data"]
            except requests.exceptions.RequestException as e:
                if attempt < max_attempts - 1:
                    self.logger.warning(f"请求失败，{delay}秒后重试 (第{attempt+1}/{max_attempts}次): {e}")
                    time.sleep(delay)
                    delay *= backoff
                else:
                    self.logger.error(f"网络请求失败: {e}")
                    raise NetworkError(f"网络请求失败: {e}")
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败: {e}")
                raise APIError(f"响应格式错误: {e}")

    def upload_file(self, file_path: str, share_expire: Optional[int] = None) -> Dict[str, str]:
        """
        上传文件并创建分享链接

        Args:
            file_path: 要上传的文件路径
            share_expire: 分享过期类型 (0:180天, 1:永久, 2:7天)，None时使用配置默认值

        Returns:
            包含分享链接和提取码的字典
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 使用配置的默认分享过期时间
        if share_expire is None:
            share_config = self.config_manager.get_share_config()
            share_expire = share_config.get("default_expire", 1)

        self.logger.info(f"开始上传文件: {file_path}")

        # ① 预上传
        upload_info = self._pre_upload(file_path)

        # ② Hash检查（关键步骤！）
        if self._check_hash(file_path, upload_info):
            self.logger.info("文件已存在，跳过上传")
            return {
                "share_url": "文件已存在，请手动创建分享链接",
                "passwd": "无",
                "title": os.path.basename(file_path),
                "status": "file_exists"
            }

        # ③ 分片上传
        etags = self._chunk_upload(file_path, upload_info)

        # ④ 提交分片
        self._commit_upload(upload_info, etags)

        # ⑤ 完成上传
        self._finish_upload(upload_info)

        # ④ 创建分享
        share_info = self._create_share(upload_info["fid"], file_path, share_expire)

        self.logger.info("文件上传和分享创建完成")
        return share_info

    def _pre_upload(self, file_path: str) -> Dict[str, Any]:
        """预上传，获取上传参数"""
        size = os.path.getsize(file_path)
        mime = mimetypes.guess_type(file_path)[0] or "application/octet-stream"

        self.logger.info(f"文件大小: {size} bytes, MIME类型: {mime}")

        pre_data = {
            "pdir_fid": self.parent_dir_id,
            "file_name": os.path.basename(file_path),
            "size": size,
            "format_type": mime,
            "ccp_hash_update": True
        }

        result = self._post("/file/upload/pre", pre_data)

        upload_info = {
            "task_id": result["task_id"],
            "upload_id": result["upload_id"],
            "bucket": result["bucket"],
            "obj_key": result["obj_key"],
            "upload_host": f"https://{result['bucket']}.{result['upload_url'][7:]}",
            "fid": result["fid"],
            "size": size,
            "auth_info": result.get("auth_info", ""),
            "callback": result.get("callback", {})
        }

        self.logger.info("预上传完成，获取上传参数")
        return upload_info

    def _check_hash(self, file_path: str, upload_info: Dict[str, Any]) -> bool:
        """检查文件hash，如果文件已存在则返回True"""
        try:
            # 计算文件的MD5和SHA1
            md5_hash, sha1_hash = self._calculate_file_hash(file_path)

            hash_data = {
                "md5": md5_hash,
                "sha1": sha1_hash,
                "task_id": upload_info["task_id"]
            }

            self.logger.info(f"检查文件hash: MD5={md5_hash[:8]}..., SHA1={sha1_hash[:8]}...")

            result = self._post("/file/update/hash", hash_data)
            finish = result.get("finish", False)

            if finish:
                self.logger.info("文件已存在，无需重复上传")
                return True
            else:
                self.logger.info("文件不存在，需要上传")
                return False

        except Exception as e:
            self.logger.warning(f"Hash检查失败，继续上传: {e}")
            return False

    def _calculate_file_hash(self, file_path: str) -> tuple:
        """计算文件的MD5和SHA1哈希值"""
        import hashlib

        md5_hash = hashlib.md5()
        sha1_hash = hashlib.sha1()

        with open(file_path, 'rb') as f:
            while chunk := f.read(8192):
                md5_hash.update(chunk)
                sha1_hash.update(chunk)

        return md5_hash.hexdigest(), sha1_hash.hexdigest()

    def _chunk_upload(self, file_path: str, upload_info: Dict[str, Any]) -> list:
        """分片上传文件，返回etags列表"""
        etags = []
        size = upload_info["size"]
        mime_type = mimetypes.guess_type(file_path)[0] or "application/octet-stream"

        with open(file_path, "rb") as f, tqdm(total=size, unit="B", unit_scale=True, desc="上传进度") as bar:
            part_num = 1
            while chunk := f.read(self.chunk_size):
                # 获取上传认证
                auth_key, time_str = self._get_upload_auth(upload_info, mime_type, part_num)
                if not auth_key:
                    raise UploadError("获取上传认证失败")

                part_url = f"{upload_info['upload_host']}/{upload_info['obj_key']}"
                params = {"partNumber": str(part_num), "uploadId": upload_info["upload_id"]}

                # 设置上传请求头（根据Go代码）
                upload_headers = {
                    "Authorization": auth_key,
                    "Content-Type": mime_type,
                    "Referer": "https://pan.quark.cn/",
                    "x-oss-date": time_str,
                    "x-oss-user-agent": "aliyun-sdk-js/6.6.1 Chrome 98.0.4758.80 on Windows 10 64-bit"
                }

                try:
                    upload_session = requests.Session()
                    response = upload_session.put(part_url, params=params, data=chunk,
                                                headers=upload_headers, timeout=self.upload_timeout)
                    response.raise_for_status()

                    etag = response.headers.get("ETag", response.headers.get("Etag", ""))
                    if not etag:
                        raise UploadError(f"未获取到ETag，分片{part_num}")

                    etags.append(etag)
                    bar.update(len(chunk))
                    part_num += 1
                except requests.exceptions.RequestException as e:
                    self.logger.error(f"分片上传失败: {e}")
                    self.logger.error(f"上传URL: {part_url}")
                    self.logger.error(f"参数: {params}")
                    raise UploadError(f"分片上传失败: {e}")

        self.logger.info(f"分片上传完成，共上传 {part_num-1} 个分片")
        return etags

    def _commit_upload(self, upload_info: Dict[str, Any], etags: list) -> None:
        """提交分片上传"""
        import hashlib
        import base64
        import json

        time_str = time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())

        # 构建XML body（根据Go代码）
        xml_parts = []
        for i, etag in enumerate(etags, 1):
            xml_parts.append(f"<Part><PartNumber>{i}</PartNumber><ETag>{etag}</ETag></Part>")

        xml_body = f"""<?xml version="1.0" encoding="UTF-8"?>
<CompleteMultipartUpload>
{"".join(xml_parts)}
</CompleteMultipartUpload>"""

        # 计算Content-MD5
        md5_hash = hashlib.md5()
        md5_hash.update(xml_body.encode('utf-8'))
        content_md5 = base64.b64encode(md5_hash.digest()).decode('utf-8')

        # 处理回调数据
        callback_json = json.dumps(upload_info["callback"])
        callback_b64 = base64.b64encode(callback_json.encode('utf-8')).decode('utf-8')

        # 构建授权元数据（根据Go代码格式）
        auth_meta = f"POST\n{content_md5}\napplication/xml\n{time_str}\nx-oss-callback:{callback_b64}\nx-oss-date:{time_str}\nx-oss-user-agent:aliyun-sdk-js/6.6.1 Chrome 98.0.4758.80 on Windows 10 64-bit\n/{upload_info['bucket']}/{upload_info['obj_key']}?uploadId={upload_info['upload_id']}"

        auth_data = {
            "auth_info": upload_info["auth_info"],
            "auth_meta": auth_meta,
            "task_id": upload_info["task_id"]
        }

        # 获取提交授权
        result = self._post("/file/upload/auth", auth_data)
        auth_key = result.get("auth_key")
        if not auth_key:
            raise UploadError("获取提交授权失败")

        # 执行提交请求
        commit_url = f"{upload_info['upload_host']}/{upload_info['obj_key']}"
        commit_headers = {
            "Authorization": auth_key,
            "Content-MD5": content_md5,
            "Content-Type": "application/xml",
            "Referer": "https://pan.quark.cn/",
            "x-oss-callback": callback_b64,
            "x-oss-date": time_str,
            "x-oss-user-agent": "aliyun-sdk-js/6.6.1 Chrome 98.0.4758.80 on Windows 10 64-bit"
        }

        params = {"uploadId": upload_info["upload_id"]}

        try:
            commit_session = requests.Session()
            response = commit_session.post(commit_url, params=params, data=xml_body,
                                         headers=commit_headers, timeout=self.upload_timeout)
            response.raise_for_status()
            self.logger.info("分片提交成功")
        except requests.exceptions.RequestException as e:
            self.logger.error(f"分片提交失败: {e}")
            raise UploadError(f"分片提交失败: {e}")

    def _get_upload_auth(self, upload_info: Dict[str, Any], mime_type: str, part_num: int) -> Optional[str]:
        """获取上传认证密钥"""
        try:
            time_str = time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())
            # 根据Go代码的正确格式
            auth_meta = f"PUT\n\n{mime_type}\n{time_str}\nx-oss-date:{time_str}\nx-oss-user-agent:aliyun-sdk-js/6.6.1 Chrome 98.0.4758.80 on Windows 10 64-bit\n/{upload_info['bucket']}/{upload_info['obj_key']}?partNumber={part_num}&uploadId={upload_info['upload_id']}"

            auth_data = {
                "auth_info": upload_info["auth_info"],
                "auth_meta": auth_meta,
                "task_id": upload_info["task_id"]
            }

            result = self._post("/file/upload/auth", auth_data)
            return result.get("auth_key"), time_str
        except Exception as e:
            self.logger.error(f"获取上传认证失败: {e}")
            return None, None

    def _finish_upload(self, upload_info: Dict[str, Any]) -> None:
        """完成上传"""
        finish_data = {
            "obj_key": upload_info["obj_key"],
            "task_id": upload_info["task_id"]
        }

        try:
            self._post("/file/upload/finish", finish_data)
            self.logger.info("上传完成")
            # 根据Go代码，需要等待1秒
            time.sleep(1)
        except Exception as e:
            # 如果finish失败，可能文件已经上传成功了
            self.logger.warning(f"完成上传API调用失败，但文件可能已上传成功: {e}")
            # 不抛出异常，继续执行

    def _create_share(self, fid: str, file_path: str, share_expire: int) -> Dict[str, str]:
        """创建分享链接（异步流程）"""
        title = os.path.basename(file_path)

        try:
            # 步骤1: 创建分享任务
            task_id = self._create_share_task(fid, title, share_expire)
            if not task_id:
                raise Exception("创建分享任务失败")

            # 步骤2: 轮询分享任务状态
            share_id = self._wait_share_task(task_id)
            if not share_id:
                raise Exception("分享任务执行失败")

            # 步骤3: 获取分享链接信息
            share_info = self._get_share_info(share_id, title)
            if not share_info:
                raise Exception("获取分享链接失败")

            self.logger.info(f"分享链接创建成功: {share_info['share_url']}")
            return share_info

        except Exception as e:
            self.logger.warning(f"分享链接创建失败: {e}")
            return {
                "share_url": "文件已上传，但分享链接创建失败",
                "passwd": "无",
                "title": title,
                "status": "upload_success_share_failed",
                "error": str(e)
            }

    def _create_share_task(self, fid: str, title: str, share_expire: int) -> Optional[str]:
        """创建分享任务"""
        share_data = {
            "fid_list": [fid],
            "title": title,
            "expired_type": share_expire,
            "url_type": 1  # 标准短链
        }

        try:
            result = self._post("/share", share_data)
            task_id = result.get("task_id")
            if task_id:
                self.logger.info(f"分享任务创建成功: {task_id}")
            return task_id
        except Exception as e:
            self.logger.error(f"创建分享任务失败: {e}")
            return None

    def _wait_share_task(self, task_id: str, max_wait: int = 60) -> Optional[str]:
        """等待分享任务完成"""
        self.logger.info("正在等待分享任务完成...")

        for i in range(max_wait):
            try:
                # 检查任务状态
                params = {"task_id": task_id, "retry_index": 0}
                result = self._get("/task", params)

                status = result.get("status")
                if status == 2:  # 成功
                    share_id = result.get("share_id")
                    if share_id:
                        self.logger.info("分享任务完成")
                        return share_id
                elif status in [3, 4]:  # 失败或取消
                    self.logger.error(f"分享任务失败: 状态码 {status}")
                    return None

                # 等待1秒后重试
                time.sleep(1)

            except Exception as e:
                self.logger.warning(f"检查分享任务状态失败: {e}")
                time.sleep(1)

        self.logger.error("分享任务等待超时")
        return None

    def _get_share_info(self, share_id: str, title: str) -> Optional[Dict[str, str]]:
        """获取分享链接信息"""
        try:
            data = {"share_id": share_id}
            result = self._post("/share/password", data)

            share_data = result.get("data", result)  # 兼容不同的响应格式

            return {
                "share_url": share_data.get("share_url", ""),
                "passwd": share_data.get("passcode", share_data.get("pwd", "无")),
                "title": title,
                "status": "success"
            }
        except Exception as e:
            self.logger.error(f"获取分享链接信息失败: {e}")
            return None

    def _get(self, path: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """发送GET请求到夸克网盘API"""
        try:
            url = self.api_base + path
            all_params = {**self.params, **params}
            response = self.session.get(url, params=all_params, timeout=self.timeout)
            response.raise_for_status()

            result = response.json()
            if result.get("code") != 0:
                error_msg = result.get("message", "未知错误")
                raise APIError(error_msg, result.get("code"))

            return result["data"]
        except requests.exceptions.RequestException as e:
            self.logger.error(f"GET请求失败: {e}")
            raise NetworkError(f"网络请求失败: {e}")
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            raise APIError(f"响应格式错误: {e}")

    def upload_files(self, file_paths: list, share_expire: Optional[int] = None) -> list:
        """
        批量上传文件并创建分享链接

        Args:
            file_paths: 要上传的文件路径列表
            share_expire: 分享过期类型

        Returns:
            包含每个文件分享信息的列表
        """
        results = []
        total_files = len(file_paths)

        self.logger.info(f"开始批量上传 {total_files} 个文件")

        for i, file_path in enumerate(file_paths, 1):
            try:
                self.logger.info(f"正在上传第 {i}/{total_files} 个文件: {file_path}")
                result = self.upload_file(file_path, share_expire)
                result["status"] = "success"
                result["file_path"] = file_path
                results.append(result)
            except Exception as e:
                self.logger.error(f"文件上传失败 {file_path}: {e}")
                results.append({
                    "status": "failed",
                    "file_path": file_path,
                    "error": str(e)
                })

        success_count = sum(1 for r in results if r["status"] == "success")
        self.logger.info(f"批量上传完成: {success_count}/{total_files} 个文件成功")

        return results

    def validate_file(self, file_path: str, raise_exception: bool = False) -> bool:
        """
        验证文件是否可以上传

        Args:
            file_path: 文件路径
            raise_exception: 是否抛出异常而不是返回False

        Returns:
            是否可以上传
        """
        try:
            if not os.path.exists(file_path):
                error_msg = f"文件不存在: {file_path}"
                self.logger.error(error_msg)
                if raise_exception:
                    raise FileValidationError(error_msg)
                return False

            if not os.path.isfile(file_path):
                error_msg = f"不是文件: {file_path}"
                self.logger.error(error_msg)
                if raise_exception:
                    raise FileValidationError(error_msg)
                return False

            file_size = os.path.getsize(file_path)
            if file_size == 0:
                error_msg = f"文件为空: {file_path}"
                self.logger.error(error_msg)
                if raise_exception:
                    raise FileValidationError(error_msg)
                return False

            # 检查文件大小限制 (夸克网盘单文件最大10GB)
            max_size = 10 * 1024 * 1024 * 1024  # 10GB
            if file_size > max_size:
                error_msg = f"文件过大 ({file_size} bytes > {max_size} bytes): {file_path}"
                self.logger.error(error_msg)
                if raise_exception:
                    raise FileValidationError(error_msg)
                return False

            return True
        except OSError as e:
            error_msg = f"文件访问错误: {e}"
            self.logger.error(error_msg)
            if raise_exception:
                raise FileValidationError(error_msg)
            return False

    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息

        Args:
            file_path: 文件路径

        Returns:
            文件信息字典
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        stat = os.stat(file_path)
        mime_type = mimetypes.guess_type(file_path)[0] or "application/octet-stream"

        return {
            "name": os.path.basename(file_path),
            "path": file_path,
            "size": stat.st_size,
            "size_human": self._format_size(stat.st_size),
            "mime_type": mime_type,
            "modified_time": time.ctime(stat.st_mtime),
            "created_time": time.ctime(stat.st_ctime)
        }

    def _format_size(self, size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"

    def create_share_only(self, fid: str, title: str, share_expire: Optional[int] = None) -> Dict[str, str]:
        """
        仅创建分享链接（文件已存在于网盘中）

        Args:
            fid: 文件ID
            title: 分享标题
            share_expire: 分享过期类型

        Returns:
            分享信息字典
        """
        if share_expire is None:
            share_config = self.config_manager.get_share_config()
            share_expire = share_config.get("default_expire", 1)

        return self._create_share(fid, title, share_expire)

    def get_share_expire_options(self) -> Dict[int, str]:
        """
        获取分享过期选项

        Returns:
            过期选项字典
        """
        return {
            0: "180天 (SVIP可1年)",
            1: "永久",
            2: "7天",
            3: "1天",
            4: "1小时"
        }

    def format_share_info(self, share_info: Dict[str, str]) -> str:
        """
        格式化分享信息为可读字符串

        Args:
            share_info: 分享信息字典

        Returns:
            格式化的分享信息
        """
        title = share_info.get("title", "未知文件")
        url = share_info.get("share_url", "")
        passwd = share_info.get("passwd", "无")

        return f"""
📁 文件名: {title}
🔗 分享链接: {url}
🔑 提取码: {passwd}
        """.strip()

    def batch_create_shares(self, file_infos: list, share_expire: Optional[int] = None) -> list:
        """
        批量创建分享链接

        Args:
            file_infos: 文件信息列表，每个元素包含fid和title
            share_expire: 分享过期类型

        Returns:
            分享信息列表
        """
        results = []
        total_files = len(file_infos)

        self.logger.info(f"开始批量创建 {total_files} 个分享链接")

        for i, file_info in enumerate(file_infos, 1):
            try:
                fid = file_info["fid"]
                title = file_info["title"]

                self.logger.info(f"正在创建第 {i}/{total_files} 个分享链接: {title}")
                result = self.create_share_only(fid, title, share_expire)
                result["status"] = "success"
                results.append(result)
            except Exception as e:
                self.logger.error(f"分享链接创建失败 {file_info.get('title', 'unknown')}: {e}")
                results.append({
                    "status": "failed",
                    "title": file_info.get("title", "unknown"),
                    "error": str(e)
                })

        success_count = sum(1 for r in results if r["status"] == "success")
        self.logger.info(f"批量分享创建完成: {success_count}/{total_files} 个成功")

        return results
