import hashlib, json, mimetypes, os, time, base64, requests, math
from tqdm import tqdm

# ======== 用户配置 ========
COOKIE = open("quark.cookie.txt").read().strip()      # 建议放到文件
PARENT_DIR_ID = "0"                                   # 0 = 根目录
FILE_PATH = r"quark.cookie.txt"
SHARE_EXPIRE = 1      # 0:180天(SVIP可1年)   1:永久   2:7天   ……

API = "https://drive.quark.cn/1/clouddrive"
HEADERS = {
    "Cookie": COOKIE,
    "Referer": "https://pan.quark.cn",
    "Origin":  "https://pan.quark.cn",
    "User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
}
PARAMS = {"pr":"ucpro","fr":"pc"}           # 必带

session = requests.Session()
session.headers.update(HEADERS)

def post(path, data):
    r = session.post(API+path, params=PARAMS, json=data, timeout=30)
    r.raise_for_status()
    js = r.json()
    if js.get("code") != 0:
        raise RuntimeError(js.get("message"))
    return js["data"]

# ---------- ① 预上传 ---------------
size = os.path.getsize(FILE_PATH)
mime = mimetypes.guess_type(FILE_PATH)[0] or "application/octet-stream"
pre = post("/file/upload/pre", {
    "pdir_fid": PARENT_DIR_ID,
    "file_name": os.path.basename(FILE_PATH),
    "size": size,
    "format_type": mime,
    "ccp_hash_update": True
})

task_id   = pre["task_id"]
upload_id = pre["upload_id"]
bucket    = pre["bucket"]
obj_key   = pre["obj_key"]
upload_host = f"https://{bucket}.{pre['upload_url'][7:]}"

# ---------- ② 分片上传 ---------------
CHUNK = 8 * 1024 * 1024          # 8 MB，官方上限 20 MB
etags = []
with open(FILE_PATH, "rb") as f, tqdm(total=size, unit="B", unit_scale=True) as bar:
    part_num = 1
    while chunk := f.read(CHUNK):
        part_url = f"{upload_host}/{obj_key}"
        params = {"partNumber":part_num, "uploadId":upload_id}
        r = session.put(part_url, params=params, data=chunk, timeout=120)
        r.raise_for_status()
        etags.append(r.headers["ETag"])
        bar.update(len(chunk))
        part_num += 1

# ---------- ③ 提交分片 ---------------
post("/file/upload/finish", {
    "obj_key": obj_key,
    "task_id": task_id
})
print("✅ 上传完成")

# ---------- ④ 创建分享 ---------------
# 这里直接取 finish 返回里的 fid；怕字段变动可再调 /file/list 搜索
fid = pre["fid"]
share = post("/share", {
    "fid_list":[fid],
    "title": os.path.basename(FILE_PATH),
    "expired_type": SHARE_EXPIRE,   # 1=永久
    "url_type":1                    # 1=标准短链
})
print(f"🎉 分享链接: {share['share_url']}  （提取码: {share.get('passwd','无')}）")
