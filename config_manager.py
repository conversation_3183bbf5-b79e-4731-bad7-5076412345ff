import yaml
import os
from typing import Dict, Any, Optional

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.yml"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            # 如果配置文件不存在，返回默认配置
            return self._get_default_config()
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                return config if config else self._get_default_config()
        except yaml.YAMLError as e:
            print(f"配置文件解析错误: {e}")
            return self._get_default_config()
        except Exception as e:
            print(f"读取配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "quark": {
                "cookie_file": "quark.cookie.txt",
                "parent_dir_id": "0",
                "chunk_size": 8388608,  # 8MB
                "timeout": 30,
                "upload_timeout": 120
            },
            "share": {
                "default_expire": 1,
                "url_type": 1
            },
            "proxy": {
                "enabled": False,
                "http": "",
                "https": ""
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(levelname)s - %(message)s",
                "to_file": False,
                "file_path": "quark.log"
            },
            "retry": {
                "max_attempts": 3,
                "delay": 1,
                "backoff": 2
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，如 "quark.cookie_file"
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self) -> None:
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get_quark_config(self) -> Dict[str, Any]:
        """获取夸克网盘相关配置"""
        return self.get("quark", {})
    
    def get_share_config(self) -> Dict[str, Any]:
        """获取分享相关配置"""
        return self.get("share", {})
    
    def get_proxy_config(self) -> Optional[Dict[str, str]]:
        """获取代理配置"""
        proxy_config = self.get("proxy", {})
        if proxy_config.get("enabled", False):
            return {
                "http": proxy_config.get("http", ""),
                "https": proxy_config.get("https", "")
            }
        return None
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get("logging", {})
    
    def get_retry_config(self) -> Dict[str, Any]:
        """获取重试配置"""
        return self.get("retry", {})
