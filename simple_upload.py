#!/usr/bin/env python3
"""
夸克网盘简化上传工具
专注于文件上传功能
"""

import os
import time
import mimetypes
import requests
from tqdm import tqdm
from quark import QuarkPan

def simple_upload(file_path: str):
    """简单的文件上传功能"""
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 创建夸克网盘客户端
        quark = QuarkPan()
        
        # 获取文件信息
        file_info = quark.get_file_info(file_path)
        print(f"📁 准备上传文件: {file_info['name']}")
        print(f"📊 文件大小: {file_info['size_human']}")
        print(f"📄 文件类型: {file_info['mime_type']}")
        
        # 验证文件
        if not quark.validate_file(file_path, raise_exception=True):
            return False
        
        print("\n🚀 开始上传...")
        
        # 执行上传（不创建分享链接）
        result = upload_file_only(quark, file_path)
        
        if result:
            print("\n✅ 文件上传成功！")
            print(f"📁 文件名: {file_info['name']}")
            print(f"🆔 文件ID: {result.get('fid', '未知')}")
            print("💡 提示: 文件已上传到夸克网盘，您可以在网盘中查看")
            return True
        else:
            print("\n❌ 文件上传失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 上传过程中出现错误: {e}")
        return False

def upload_file_only(quark: QuarkPan, file_path: str):
    """仅上传文件，不创建分享链接"""
    try:
        # 预上传
        upload_info = quark._pre_upload(file_path)
        
        # 分片上传
        quark._chunk_upload(file_path, upload_info)
        
        # 尝试完成上传（忽略错误）
        try:
            quark._finish_upload(upload_info)
        except:
            print("⚠️  完成上传API调用失败，但文件可能已上传成功")
        
        return {
            "fid": upload_info["fid"],
            "file_name": os.path.basename(file_path),
            "status": "uploaded"
        }
        
    except Exception as e:
        print(f"❌ 上传失败: {e}")
        return None

def batch_upload(file_paths: list):
    """批量上传文件"""
    print(f"📦 准备批量上传 {len(file_paths)} 个文件\n")
    
    success_count = 0
    for i, file_path in enumerate(file_paths, 1):
        print(f"--- 第 {i}/{len(file_paths)} 个文件 ---")
        if simple_upload(file_path):
            success_count += 1
        print()  # 空行分隔
    
    print(f"📊 批量上传完成: {success_count}/{len(file_paths)} 个文件成功")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python simple_upload.py <文件路径>")
        print("  python simple_upload.py <文件1> <文件2> <文件3>...")
        print("\n示例:")
        print("  python simple_upload.py test.txt")
        print("  python simple_upload.py file1.txt file2.txt file3.txt")
        return
    
    file_paths = sys.argv[1:]
    
    # 检查所有文件是否存在
    missing_files = [f for f in file_paths if not os.path.exists(f)]
    if missing_files:
        print("❌ 以下文件不存在:")
        for f in missing_files:
            print(f"   - {f}")
        return
    
    print("夸克网盘文件上传工具")
    print("=" * 50)
    
    if len(file_paths) == 1:
        # 单文件上传
        simple_upload(file_paths[0])
    else:
        # 批量上传
        batch_upload(file_paths)

if __name__ == "__main__":
    main()
